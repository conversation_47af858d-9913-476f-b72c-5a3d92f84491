<!doctype html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />

    <!-- PWA Meta Tags -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#693EFF" />
    <meta name="background-color" content="#FFFFFF" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="PPWA" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-TileColor" content="#693EFF" />
    <meta name="msapplication-tap-highlight" content="no" />

    <!-- SEO Meta Tags -->
    <title>PPWA - Progressive Web App</title>
    <meta name="description" content="Progressive Web App dengan React, Capacitor, dan offline capabilities untuk Android deployment" />
    <meta name="keywords" content="PWA, Progressive Web App, React, Capacitor, Android, Offline" />
    <meta name="author" content="PPWA Team" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="PPWA - Progressive Web App" />
    <meta property="og:description" content="Progressive Web App dengan React, Capacitor, dan offline capabilities" />
    <meta property="og:image" content="/images/icons/icon-512x512.png" />
    <meta property="og:url" content="https://ppwa.app" />
    <meta property="og:site_name" content="PPWA" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="PPWA - Progressive Web App" />
    <meta name="twitter:description" content="Progressive Web App dengan React, Capacitor, dan offline capabilities" />
    <meta name="twitter:image" content="/images/icons/icon-512x512.png" />

    <!-- Icons and Manifest -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="152x152" href="/images/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-192x192.png" />

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileImage" content="/images/icons/icon-144x144.png" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- Preload Critical Resources -->
    <link rel="preload" href="data:application/octet-stream;base64,aW1wb3J0IHsgU3RyaWN0TW9kZSB9IGZyb20gJ3JlYWN0JwppbXBvcnQgeyBjcmVhdGVSb290IH0gZnJvbSAncmVhY3QtZG9tL2NsaWVudCcKaW1wb3J0ICcuL2luZGV4LmNzcycKaW1wb3J0IEFwcCBmcm9tICcuL0FwcC50c3gnCgpjcmVhdGVSb290KGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdyb290JykhKS5yZW5kZXIoCiAgPFN0cmljdE1vZGU+CiAgICA8QXBwIC8+CiAgPC9TdHJpY3RNb2RlPiwKKQo=" as="script" />
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" as="style" />

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <script type="module" crossorigin src="/assets/index-DggGL3aj.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-CJsO8wys.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
