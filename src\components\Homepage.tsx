import React, { useRef, useState } from 'react';
import './Homepage.css';

/**
 * Interface untuk data produk carousel
 * Mendefinisikan struktur data untuk setiap item produk
 */
interface ProductItem {
  id: number;
  image: string;
  title: string;
  topic: string;
  description: string;
  detailTitle: string;
  detailDescription: string;
  specifications: {
    usedTime: string;
    chargingPort: string;
    compatible: string;
    bluetooth: string;
    controlled: string;
  };
}

/**
 * Komponen Homepage dengan carousel produk
 * Menampilkan slider produk dengan animasi dan detail produk
 */
const Homepage: React.FC = () => {
  // State untuk mengontrol carousel
  const [isShowingDetail, setIsShowingDetail] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  
  // Refs untuk manipulasi DOM
  const carouselRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Data produk untuk carousel
  const products: ProductItem[] = [
    {
      id: 1,
      image: '/images/product/img1.png',
      title: 'DESIGN SLIDER',
      topic: 'Aerphone',
      description: 'Headphone wireless premium dengan kualitas suara terbaik dan desain yang elegan untuk pengalaman audio yang luar biasa.',
      detailTitle: 'Aerphone GHTK',
      detailDescription: 'Headphone wireless dengan teknologi terdepan yang memberikan kualitas suara crystal clear dan bass yang mendalam. Dilengkapi dengan noise cancellation dan desain ergonomis untuk kenyamanan maksimal.',
      specifications: {
        usedTime: '6 hours',
        chargingPort: 'Type-C',
        compatible: 'Android',
        bluetooth: '5.3',
        controlled: 'Touch'
      }
    },
    {
      id: 2,
      image: '/images/product/img2.png',
      title: 'DESIGN SLIDER',
      topic: 'Aerphone Pro',
      description: 'Versi pro dengan fitur advanced dan daya tahan baterai yang lebih lama untuk profesional dan audiophile sejati.',
      detailTitle: 'Aerphone Pro GHTK',
      detailDescription: 'Headphone wireless profesional dengan driver premium dan teknologi adaptive noise cancellation. Cocok untuk studio recording dan penggunaan profesional dengan kualitas audio studio-grade.',
      specifications: {
        usedTime: '8 hours',
        chargingPort: 'Type-C',
        compatible: 'Android/iOS',
        bluetooth: '5.3',
        controlled: 'Touch'
      }
    },
    {
      id: 3,
      image: '/images/product/img3.png',
      title: 'DESIGN SLIDER',
      topic: 'Aerphone Max',
      description: 'Model flagship dengan teknologi terdepan dan fitur premium untuk pengalaman audio yang tak terlupakan.',
      detailTitle: 'Aerphone Max GHTK',
      detailDescription: 'Headphone wireless flagship dengan teknologi AI-powered audio enhancement dan adaptive EQ. Memberikan pengalaman audio yang dipersonalisasi sesuai dengan preferensi pengguna.',
      specifications: {
        usedTime: '12 hours',
        chargingPort: 'Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: 'Touch & Voice'
      }
    },
    {
      id: 4,
      image: '/images/product/img4.png',
      title: 'DESIGN SLIDER',
      topic: 'Aerphone Elite',
      description: 'Edisi terbatas dengan teknologi revolusioner dan desain eksklusif untuk para enthusiast audio premium.',
      detailTitle: 'Aerphone Elite GHTK',
      detailDescription: 'Headphone wireless edisi terbatas dengan teknologi revolusioner dan material premium. Dilengkapi dengan AI assistant dan wireless charging untuk pengalaman audio masa depan.',
      specifications: {
        usedTime: '24 hours',
        chargingPort: 'Wireless/Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: 'AI Touch & Voice'
      }
    }
  ];

  /**
   * Fungsi untuk menggeser carousel ke slide berikutnya atau sebelumnya
   * @param direction - 'next' untuk slide berikutnya, 'prev' untuk slide sebelumnya
   */
  const showSlider = (direction: 'next' | 'prev') => {
    if (isAnimating || !listRef.current || !carouselRef.current) return;

    setIsAnimating(true);

    const items = listRef.current.querySelectorAll('.carousel-item') as any;
    const carousel = carouselRef.current as any;

    // Hapus kelas animasi sebelumnya
    carousel.classList.remove('next', 'prev');

    if (direction === 'next') {
      // Pindahkan item pertama ke akhir
      listRef.current.appendChild(items[0]);
      carousel.classList.add('next');
    } else {
      // Pindahkan item terakhir ke awal
      listRef.current.prepend(items[items.length - 1]);
      carousel.classList.add('prev');
    }

    // Reset animasi setelah 2 detik
    setTimeout(() => {
      setIsAnimating(false);
    }, 2000);
  };

  /**
   * Fungsi untuk menampilkan detail produk
   */
  const showDetail = () => {
    if (!carouselRef.current) return;

    (carouselRef.current as any).classList.remove('next', 'prev');
    (carouselRef.current as any).classList.add('showDetail');
    setIsShowingDetail(true);
  };

  /**
   * Fungsi untuk kembali ke tampilan carousel
   */
  const hideDetail = () => {
    if (!carouselRef.current) return;

    (carouselRef.current as any).classList.remove('showDetail');
    setIsShowingDetail(false);
  };

  return (
    <div className="homepage-container">
      {/* Header dengan logo perusahaan */}
      <header className="header">
        <div className="company-header">
          <img
            src="/images/icons/icon-192x192.png"
            alt="PT Putera Wibowo Borneo Logo"
            className="company-logo"
          />
          <div className="company-name">PT Putera Wibowo Borneo</div>
        </div>
      </header>

      {/* Divider SVG */}
      <div className="divider-container">
        <svg xmlns="http://www.w3.org/2000/svg" className="divider-svg" viewBox="0 0 1080 137" preserveAspectRatio="none">
          <path d="M 0,137 V 59.03716 c 158.97703,52.21241 257.17659,0.48065 375.35967,2.17167 118.18308,1.69101 168.54911,29.1665 243.12679,30.10771 C 693.06415,92.25775 855.93515,29.278599 1080,73.61449 V 137 Z" style={{opacity: 0.85}} />
          <path d="M 0,10.174557 C 83.419822,8.**********.65911,41.78116 204.11379,44.65308 290.56846,47.52499 396.02558,-7.4328 620.04248,94.40134 782.19141,29.**********.67279,15.823104 1080,98.55518 V 137 H 0 Z" style={{opacity: 0.5}} />
          <path d="M 0,45.10182 C 216.27861,-66.**********.90348,63.09813 416.42665,63.52904 504.94982,63.95995 530.42054,22.**********.37532,25.**********.33012,28.**********.77619,132.60682 1080,31.125744 V 137 H 0 Z" style={{opacity: 0.25}} />
        </svg>
      </div>

      {/* Carousel utama */}
      <div ref={carouselRef} className="carousel">
        <div ref={listRef} className="carousel-list">
          {products.map((product) => (
            <div key={product.id} className="carousel-item">
              <img src={product.image} alt={product.topic} className="product-image" />
              
              {/* Konten perkenalan produk */}
              <div className="introduce">
                <div className="title">{product.title}</div>
                <div className="topic">{product.topic}</div>
                <div className="description">{product.description}</div>
                <button className="see-more-btn" onClick={showDetail}>
                  SEE MORE &#8599;
                </button>
              </div>

              {/* Detail produk */}
              <div className="detail">
                <div className="detail-title">{product.detailTitle}</div>
                <div className="detail-description">{product.detailDescription}</div>
                
                {/* Spesifikasi produk */}
                <div className="specifications">
                  <div className="spec-item">
                    <p>Used Time</p>
                    <p>{product.specifications.usedTime}</p>
                  </div>
                  <div className="spec-item">
                    <p>Charging Port</p>
                    <p>{product.specifications.chargingPort}</p>
                  </div>
                  <div className="spec-item">
                    <p>Compatible</p>
                    <p>{product.specifications.compatible}</p>
                  </div>
                  <div className="spec-item">
                    <p>Bluetooth</p>
                    <p>{product.specifications.bluetooth}</p>
                  </div>
                  <div className="spec-item">
                    <p>Controlled</p>
                    <p>{product.specifications.controlled}</p>
                  </div>
                </div>

                {/* Tombol checkout */}
                <div className="checkout">
                  <button className="btn btn-outline">ADD TO CART</button>
                  <button className="btn btn-primary">CHECKOUT</button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Tombol navigasi */}
        <div className="arrows">
          <button 
            id="prev" 
            className="nav-btn"
            onClick={() => showSlider('prev')}
            disabled={isAnimating}
          >
            &#8249;
          </button>
          <button 
            id="next" 
            className="nav-btn"
            onClick={() => showSlider('next')}
            disabled={isAnimating}
          >
            &#8250;
          </button>
          <button 
            id="back" 
            className={`back-btn ${isShowingDetail ? 'visible' : ''}`}
            onClick={hideDetail}
          >
            See All &#8599;
          </button>
        </div>
      </div>
    </div>
  );
};

export default Homepage;
