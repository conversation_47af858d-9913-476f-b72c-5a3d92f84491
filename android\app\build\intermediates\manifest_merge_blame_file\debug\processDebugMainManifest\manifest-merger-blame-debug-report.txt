1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ppwa.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions untuk PPWA -->
12    <!-- Location permissions -->
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:6:5-81
13-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:6:22-78
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:7:5-79
14-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:7:22-76
15
16    <uses-feature
16-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:8:5-87
17        android:name="android.hardware.location"
17-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:8:19-59
18        android:required="false" />
18-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:8:60-84
19    <uses-feature
19-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:9:5-91
20        android:name="android.hardware.location.gps"
20-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:9:19-63
21        android:required="false" />
21-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:9:64-88
22    <uses-feature
22-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:10:5-95
23        android:name="android.hardware.location.network"
23-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:10:19-67
24        android:required="false" />
24-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:10:68-92
25
26    <!-- Network permissions -->
27    <uses-permission android:name="android.permission.INTERNET" />
27-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:13:5-67
27-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:13:22-64
28    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
28-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:14:5-79
28-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:14:22-76
29    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
29-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:15:5-76
29-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:15:22-73
30
31    <!-- Device information permissions -->
32    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
32-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:18:5-75
32-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:18:22-72
33
34    <!-- Storage permissions untuk offline data -->
35    <uses-permission
35-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:21:5-22:51
36        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
36-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:21:22-78
37        android:maxSdkVersion="28" />
37-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:22:22-48
38    <uses-permission
38-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:23:5-24:51
39        android:name="android.permission.READ_EXTERNAL_STORAGE"
39-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:23:22-77
40        android:maxSdkVersion="32" />
40-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:24:22-48
41
42    <!-- Notification permissions -->
43    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
43-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:27:5-77
43-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:27:22-74
44    <uses-permission android:name="android.permission.VIBRATE" />
44-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:28:5-66
44-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:28:22-63
45
46    <!-- Wake lock untuk background sync -->
47    <uses-permission android:name="android.permission.WAKE_LOCK" />
47-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:31:5-68
47-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:31:22-65
48
49    <!-- Foreground service untuk background sync -->
50    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
50-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:34:5-77
50-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:34:22-74
51
52    <!-- Permissions -->
53
54    <uses-permission android:name="android.permission.INTERNET" />
54-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:13:5-67
54-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:13:22-64
55    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
55-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
55-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
56
57    <permission
57-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
58        android:name="com.ppwa.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
58-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
59        android:protectionLevel="signature" />
59-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
60
61    <uses-permission android:name="com.ppwa.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
61-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
61-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
62
63    <application
63-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:36:5-68:19
64        android:allowBackup="true"
64-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:37:9-35
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
66        android:debuggable="true"
67        android:extractNativeLibs="false"
68        android:icon="@mipmap/ic_launcher"
68-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:38:9-43
69        android:label="@string/app_name"
69-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:39:9-41
70        android:roundIcon="@mipmap/ic_launcher_round"
70-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:40:9-54
71        android:supportsRtl="true"
71-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:41:9-35
72        android:testOnly="true"
73        android:theme="@style/AppTheme"
73-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:42:9-40
74        android:usesCleartextTraffic="true" >
74-->[:capacitor-cordova-android-plugins] C:\xampp\htdocs\ppwa\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-53
75        <activity
75-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:44:9-57:20
76            android:name="com.ppwa.app.MainActivity"
76-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:46:13-41
77            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
77-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:45:13-140
78            android:exported="true"
78-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:50:13-36
79            android:label="@string/title_activity_main"
79-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:47:13-56
80            android:launchMode="singleTask"
80-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:49:13-44
81            android:theme="@style/AppTheme.NoActionBarLaunch" >
81-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:48:13-62
82            <intent-filter>
82-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:52:13-55:29
83                <action android:name="android.intent.action.MAIN" />
83-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:53:17-69
83-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:53:25-66
84
85                <category android:name="android.intent.category.LAUNCHER" />
85-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:54:17-77
85-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:54:27-74
86            </intent-filter>
87        </activity>
88
89        <provider
90            android:name="androidx.core.content.FileProvider"
90-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:60:13-62
91            android:authorities="com.ppwa.app.fileprovider"
91-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:61:13-64
92            android:exported="false"
92-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:62:13-37
93            android:grantUriPermissions="true" >
93-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:63:13-47
94            <meta-data
94-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:64:13-66:64
95                android:name="android.support.FILE_PROVIDER_PATHS"
95-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:65:17-67
96                android:resource="@xml/file_paths" />
96-->C:\xampp\htdocs\ppwa\android\app\src\main\AndroidManifest.xml:66:17-51
97        </provider>
98
99        <receiver android:name="com.capacitorjs.plugins.localnotifications.TimedNotificationPublisher" />
99-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-106
99-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:19-103
100        <receiver android:name="com.capacitorjs.plugins.localnotifications.NotificationDismissReceiver" />
100-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-107
100-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:19-104
101        <receiver
101-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-23:20
102            android:name="com.capacitorjs.plugins.localnotifications.LocalNotificationRestoreReceiver"
102-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-103
103            android:directBootAware="true"
103-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-43
104            android:exported="false" >
104-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
105            <intent-filter>
105-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-22:29
106                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
106-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-86
106-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-83
107                <action android:name="android.intent.action.BOOT_COMPLETED" />
107-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-79
107-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:25-76
108                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
108-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:17-82
108-->[:capacitor-local-notifications] C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:25-79
109            </intent-filter>
110        </receiver>
111
112        <activity
112-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
113            android:name="com.google.android.gms.common.api.GoogleApiActivity"
113-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
114            android:exported="false"
114-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
115            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
115-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
116
117        <provider
117-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
118            android:name="androidx.startup.InitializationProvider"
118-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
119            android:authorities="com.ppwa.app.androidx-startup"
119-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
120            android:exported="false" >
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
121            <meta-data
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.emoji2.text.EmojiCompatInitializer"
122-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
123                android:value="androidx.startup" />
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
124            <meta-data
124-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
125-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
126                android:value="androidx.startup" />
126-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
129                android:value="androidx.startup" />
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
130        </provider>
131
132        <meta-data
132-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
133            android:name="com.google.android.gms.version"
133-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
134            android:value="@integer/google_play_services_version" />
134-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
135
136        <receiver
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
137            android:name="androidx.profileinstaller.ProfileInstallReceiver"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
138            android:directBootAware="false"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
139            android:enabled="true"
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
140            android:exported="true"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
141            android:permission="android.permission.DUMP" >
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
142            <intent-filter>
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
143                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
144            </intent-filter>
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
146                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
147            </intent-filter>
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
149                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
150            </intent-filter>
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
152                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
153            </intent-filter>
154        </receiver>
155    </application>
156
157</manifest>
