import React, { useState } from 'react';
import './Navigation.css';

/**
 * Interface untuk item navigasi
 * Mendefinisikan struktur data untuk setiap tombol navigasi
 */
interface NavigationItem {
  id: string;
  label: string;
  icon?: string;
  path: string;
}

/**
 * Props untuk komponen Navigation
 */
interface NavigationProps {
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
}

/**
 * Komponen Navigation horizontal dengan DaisyUI
 * Menampilkan tombol navigasi untuk HOME, KALENDER, PROFILE, INFORMATION, PRODUK, LAYANAN
 */
const Navigation: React.FC<NavigationProps> = ({ 
  activeTab = 'home', 
  onTabChange 
}) => {
  const [currentTab, setCurrentTab] = useState(activeTab);

  // Data navigasi dengan ikon dan label
  const navigationItems: NavigationItem[] = [
    {
      id: 'home',
      label: 'HOME',
      icon: '🏠',
      path: '/home'
    },
    {
      id: 'kalender',
      label: 'KALENDER',
      icon: '📅',
      path: '/kalender'
    },
    {
      id: 'profile',
      label: 'PROFILE',
      icon: '👤',
      path: '/profile'
    },
    {
      id: 'information',
      label: 'INFORMATION',
      icon: 'ℹ️',
      path: '/information'
    },
    {
      id: 'produk',
      label: 'PRODUK',
      icon: '📦',
      path: '/produk'
    },
    {
      id: 'layanan',
      label: 'LAYANAN',
      icon: '🛠️',
      path: '/layanan'
    }
  ];

  /**
   * Fungsi untuk menangani perubahan tab navigasi
   * @param tabId - ID tab yang dipilih
   */
  const handleTabChange = (tabId: string) => {
    setCurrentTab(tabId);
    if (onTabChange) {
      (onTabChange as any)(tabId);
    }
  };

  return (
    <div className="navigation-container">
      {/* Divider untuk memisahkan konten utama dengan navigasi */}
      <div className="divider"></div>

      {/* Container navigasi */}
      <div className="container mx-auto px-2">
        <div className="tabs">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              className={`tab ${currentTab === item.id ? 'tab-active' : ''}`}
              onClick={() => handleTabChange(item.id)}
              aria-label={`Navigasi ke ${item.label}`}
            >
              {/* Ikon navigasi */}
              <span>{item.icon}</span>

              {/* Label navigasi */}
              <span>{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Navigation;
